import { useState, useEffect } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { GardenPlant, Plant } from '@/types/plant';
import { PlantIdentification, PlantDiagnosis } from '@/services/database';
import { useAuth } from './useAuth';
import { useDatabase } from './useDatabase';

// Note: Garden collections table has been removed. Garden data is now stored directly
// in plant_identifications and plant_diagnoses tables with garden-specific fields.

// Helper function to map diagnosis severity to health status
const mapSeverityToHealthStatus = (severity?: string): 'healthy' | 'sick' | 'recovering' | 'critical' => {
  if (!severity) return 'healthy';

  const normalizedSeverity = severity.toLowerCase().trim();

  switch (normalizedSeverity) {
    case 'mild':
    case 'moderate':
    case 'medium':
      return 'sick';
    case 'severe':
    case 'critical':
    case 'high':
      return 'critical';
    case 'healthy':
    case 'none':
    case 'no issues':
    case 'no problems':
    case 'good':
    case 'fine':
    case 'normal':
    case 'low':
    case 'not applicable':
    case 'n/a':
      return 'healthy';
    default:
      // If we can't determine the severity, default to healthy for diagnosed plants
      // since the user specifically asked for a diagnosis
      return 'healthy';
  }
};

// convertToGardenPlant function removed - garden_collections table no longer exists

// Helper function to convert PlantIdentification to GardenPlant (new structure)
const convertIdentificationToGardenPlant = (identification: PlantIdentification): GardenPlant => {
  // Parse care instructions from JSON string if it's a string
  let careInstructions;
  try {
    if (typeof identification.care_instructions === 'string') {
      careInstructions = JSON.parse(identification.care_instructions);
    } else {
      careInstructions = identification.care_instructions;
    }
  } catch (error) {
    console.error('Error parsing care instructions:', error);
    careInstructions = {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    };
  }

  return {
    id: identification.id,
    scientificName: identification.scientific_name || 'Unknown',
    commonName: identification.common_name || 'Unknown Plant',
    imageUrl: identification.image_url || '',
    description: identification.description || '',
    careInstructions: careInstructions || {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    },
    tags: identification.tags || [],
    addedDate: new Date(identification.created_at),
    createdAt: new Date(identification.created_at),
    updatedAt: new Date(identification.updated_at),
    nickname: identification.nickname,
    location: identification.location_taken,
    locationInGarden: identification.location_in_garden,
    healthStatus: identification.health_status,
    lastWatered: identification.last_watered ? new Date(identification.last_watered) : undefined,
    lastFertilized: identification.last_fertilized ? new Date(identification.last_fertilized) : undefined,
    notes: identification.notes,
    isPublic: identification.is_public,
    allowCommunityTips: identification.allow_community_tips,
    plantIdentificationId: identification.id,
    // Plant details
    plantType: identification.plant_type,
    nativeRegion: identification.native_region,
    toxicityLevel: identification.toxicity_level,
    toxicityWarning: identification.toxicity_warning,
    growthHabit: identification.growth_habit,
    growthRate: identification.growth_rate,
    matureHeight: identification.mature_height,
    matureWidth: identification.mature_width,
    matureDescription: identification.mature_description,
    bloomTime: identification.bloom_time,
    flowerColors: identification.flower_colors,
    foliageType: identification.foliage_type,
    hardinessZones: identification.hardiness_zones,
    minTemperature: identification.min_temperature,
    pestsAndDiseases: identification.pests_and_diseases,
    funFacts: identification.fun_facts,
    uses: identification.uses,
    propagation: identification.propagation,
    seasonalCare: identification.seasonal_care,
    companionPlants: identification.companion_plants,
    maintenanceLevel: identification.maintenance_level,
  };
};

// Helper function to convert PlantDiagnosis to GardenPlant (new structure)
const convertDiagnosisToGardenPlant = (diagnosis: PlantDiagnosis): GardenPlant => {
  // Plant identification fields are now stored directly in plant_diagnoses table
  // No need to rely on joined plant_identifications data

  // Parse care instructions from diagnosis, else from identification, else defaults
  let careInstructions: any;
  try {
    if (typeof diagnosis.care_instructions === 'string') {
      careInstructions = JSON.parse(diagnosis.care_instructions);
    } else if (diagnosis.care_instructions) {
      careInstructions = diagnosis.care_instructions;
    } else {
      // Default care instructions if none provided
      careInstructions = {
        light: 'medium',
        water: 'medium',
        temperature: { min: 18, max: 25, unit: 'C' },
        humidity: 'medium',
        soil: 'Well-draining potting mix',
        fertilizer: 'Monthly during growing season',
        toxicity: 'none'
      };
    }
  } catch (error) {
    console.error('Error parsing care instructions:', error);
    careInstructions = {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    };
  }

  return {
    id: diagnosis.id,
    scientificName: diagnosis.scientific_name || 'Unknown',
    commonName: diagnosis.common_name || diagnosis.nickname || 'Unknown Plant',
    imageUrl: diagnosis.image_url || '',
    description: diagnosis.description || 'Plant diagnosis record',
    careInstructions: careInstructions || {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    },
    tags: Array.isArray(diagnosis.tags) ? diagnosis.tags : [],
    // Use confidence from diagnosis
    confidence: diagnosis.confidence_score || 0.95,
    // Plant attributes stored directly in diagnosis
    plantType: diagnosis.plant_type,
    nativeRegion: diagnosis.native_region,
    growthHabit: diagnosis.growth_habit,
    growthRate: diagnosis.growth_rate,
    toxicityLevel: diagnosis.toxicity_level,
    toxicityWarning: diagnosis.toxicity_warning,
    matureHeight: diagnosis.mature_height,
    matureWidth: diagnosis.mature_width,
    matureDescription: diagnosis.mature_description,
    bloomTime: diagnosis.bloom_time,
    flowerColors: Array.isArray(diagnosis.flower_colors) ? diagnosis.flower_colors : [],
    foliageType: diagnosis.foliage_type,
    hardinessZones: diagnosis.hardiness_zones,
    minTemperature: diagnosis.min_temperature,
    pestsAndDiseases: diagnosis.pests_and_diseases,
    funFacts: Array.isArray(diagnosis.fun_facts) ? diagnosis.fun_facts : [],
    uses: Array.isArray(diagnosis.uses) ? diagnosis.uses : [],
    propagation: diagnosis.propagation,
    seasonalCare: diagnosis.seasonal_care,
    companionPlants: Array.isArray(diagnosis.companion_plants) ? diagnosis.companion_plants : [],
    maintenanceLevel: diagnosis.maintenance_level,
    addedDate: new Date(diagnosis.created_at),
    createdAt: new Date(diagnosis.created_at),
    updatedAt: new Date(diagnosis.updated_at),
    nickname: diagnosis.nickname,
    location: diagnosis.location,
    locationInGarden: diagnosis.location_in_garden,
    healthStatus: diagnosis.health_status ?? mapSeverityToHealthStatus(diagnosis.severity),
    lastWatered: diagnosis.last_watered ? new Date(diagnosis.last_watered) : undefined,
    lastFertilized: diagnosis.last_fertilized ? new Date(diagnosis.last_fertilized) : undefined,
    notes: diagnosis.notes,
    isPublic: diagnosis.is_public,
    allowCommunityTips: diagnosis.allow_community_tips,
    plantIdentificationId: diagnosis.plant_identification_id,
    // Diagnosis data
    diagnosis: {
      id: diagnosis.id,
      diagnosedProblem: diagnosis.diagnosed_problem,
      likelyCauses: diagnosis.likely_causes,
      symptomsObserved: diagnosis.symptoms_observed,
      severity: diagnosis.severity,
      immediateActions: diagnosis.immediate_actions,
      longTermCare: diagnosis.long_term_care,
      productRecommendations: diagnosis.product_recommendations,
      stepByStepInstructions: diagnosis.step_by_step_instructions,
      preventionTips: diagnosis.prevention_tips,
      prognosis: diagnosis.prognosis,
      createdAt: new Date(diagnosis.created_at),
    },
  };
};

export const [GardenProvider, useGarden] = createContextHook(() => {
  const [plants, setPlants] = useState<GardenPlant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const { getPlantIdentifications, getPlantDiagnoses } = useDatabase();

  // Load garden plants from database
  useEffect(() => {
    const loadGarden = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        // Load plant identifications (identified plants)
        const identifications = await getPlantIdentifications(user.id);
        const identifiedPlants = identifications.map(convertIdentificationToGardenPlant);

        // Load plant diagnoses (diagnosed plants)
        const diagnoses = await getPlantDiagnoses(user.id);
        const diagnosedPlants = diagnoses.map(convertDiagnosisToGardenPlant);

        // Combine identified and diagnosed plants
        const allPlants = [...identifiedPlants, ...diagnosedPlants];
        setPlants(allPlants);
      } catch (error) {
        console.error('Error loading garden:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGarden();
  }, [user, getPlantIdentifications, getPlantDiagnoses]);

  const {
    getPlantDiagnosis,
    createIdentificationAndAddToGarden,
    createPlantIdentification,
    createPlantDiagnosis,
    updatePlantIdentification,
    updatePlantDiagnosis,
    deletePlantIdentification,
    deletePlantDiagnosis,
    updateDiagnosisNotes,
    shareIdentificationOnly,
    shareDiagnosisOnly
  } = useDatabase();

  const addPlant = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string) => {
    if (!user) return;

    try {
      // Get diagnosis data if diagnosisId is provided to determine health status
      let healthStatus: 'healthy' | 'sick' | 'recovering' | 'critical' = 'healthy';
      if (diagnosisId) {
        try {
          const diagnosis = await getPlantDiagnosis(diagnosisId);
          if (diagnosis) {
            healthStatus = mapSeverityToHealthStatus(diagnosis.severity);
          }
        } catch (error) {
          // Continue with default 'healthy' status
        }
      }

      // Create plant identification with garden fields (private)
      const identification = await createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: false, // Private - "Keep to myself"
        location_taken: location,
        // Plant details
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
        // Garden fields (moved from garden_collections)
        notes: notes || '',
        nickname: nickname || plant.commonName,
        health_status: healthStatus,
        location_in_garden: location,
        allow_community_tips: false, // Private plants don't allow community tips
      });

      if (identification) {
        // Convert to GardenPlant and add to state
        const gardenPlant = convertIdentificationToGardenPlant(identification);
        setPlants((current) => [gardenPlant, ...current]);
        // console.log('Plant added to your garden successfully!');
      }
    } catch (error) {
      console.error('Error adding plant to garden:', error);
      throw error;
    }
  };

  const removePlant = async (plantId: string) => {
    try {
      // Find the plant to determine if it's an identification or diagnosis
      const plant = plants.find(p => p.id === plantId);
      if (!plant) {
        throw new Error('Plant not found');
      }

      let success = false;
      if (plant.diagnosis) {
        // It's a diagnosis, remove from plant_diagnoses table
        success = await deletePlantDiagnosis(plantId);
      } else {
        // It's an identification, remove from plant_identifications table
        success = await deletePlantIdentification(plantId);
      }

      if (success) {
        setPlants((current) => current.filter((plant) => plant.id !== plantId));
        return true;
      } else {
        throw new Error('Failed to remove plant from database');
      }
    } catch (error) {
      throw error; // Re-throw to let the calling component handle the error
    }
  };

  const waterPlant = async (plantId: string) => {
    try {
      await updatePlant(plantId, {
        last_watered: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error updating plant watering:', error);
      throw error;
    }
  };

  const updatePlant = async (plantId: string, updates: any) => {
    try {
      // Find the plant to determine if it's an identification or diagnosis
      const plant = plants.find(p => p.id === plantId);
      if (!plant) {
        throw new Error('Plant not found');
      }

      let updatedRecord = null;
      if (plant.diagnosis) {
        // It's a diagnosis, update plant_diagnoses table
        updatedRecord = await updatePlantDiagnosis(plantId, updates);
        if (updatedRecord) {
          const updatedPlant = convertDiagnosisToGardenPlant(updatedRecord);
          setPlants((current) =>
            current.map((p) => p.id === plantId ? updatedPlant : p)
          );
        }
      } else {
        // It's an identification, update plant_identifications table
        updatedRecord = await updatePlantIdentification(plantId, updates);
        if (updatedRecord) {
          const updatedPlant = convertIdentificationToGardenPlant(updatedRecord);
          setPlants((current) =>
            current.map((p) => p.id === plantId ? updatedPlant : p)
          );
        }
      }
    } catch (error) {
      console.error('Error updating plant:', error);
      throw error;
    }
  };

  // Update diagnosis notes for diagnosed plants
  const updateDiagnosisNotesLocal = async (diagnosisId: string, notes: string) => {
    try {
      const updatedDiagnosis = await updateDiagnosisNotes(diagnosisId, notes);

      if (updatedDiagnosis) {
        // Update the plant in the local state
        setPlants((current) =>
          current.map((plant) => {
            if (plant.diagnosis?.id === diagnosisId) {
              return {
                ...plant,
                notes: notes, // Update the notes in the plant object
                diagnosis: {
                  ...plant.diagnosis,
                  // Keep all diagnosis data the same
                }
              };
            }
            return plant;
          })
        );
      }
    } catch (error) {
      console.error('Error updating diagnosis notes:', error);
      throw error;
    }
  };

  const shareGardenItem = async (plantId: string, isPublic: boolean, shareSettings: any) => {
    try {
      const updates = {
        is_public: isPublic,
        allow_community_tips: shareSettings.allowCommunityTips,
      };

      await updatePlant(plantId, updates);
    } catch (error) {
      throw error;
    }
  };

  const saveDiagnosisOnly = async (plant: Plant, diagnosisData?: any, isPublic: boolean = false) => {
    if (!user) return;

    try {
      // Create plant identification first
      const identification = await createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: isPublic,
        // Add all the new fields from the Plant type
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        throw new Error('Failed to create plant identification');
      }

      // Create diagnosis
      if (diagnosisData) {
        const normalizedSeverity = diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical';
        const savedDiagnosis = await createPlantDiagnosis({
          user_id: user.id,
          plant_identification_id: identification.id,
          image_url: plant.imageUrl,
          problem_description: diagnosisData.problemDescription,
          diagnosed_problem: diagnosisData.diagnosedProblem,
          likely_causes: diagnosisData.likelyCauses,
          symptoms_observed: diagnosisData.symptomsObserved,
          severity: normalizedSeverity,
          immediate_actions: diagnosisData.immediateActions,
          long_term_care: diagnosisData.longTermCare,
          product_recommendations: diagnosisData.productRecommendations,
          step_by_step_instructions: diagnosisData.stepByStepInstructions,
          prevention_tips: diagnosisData.preventionTips,
          prognosis: diagnosisData.prognosis,
          confidence_score: diagnosisData.confidence || 0.95,
          diagnosis_source: 'openrouter_api',
          is_verified: false,
          is_public: isPublic,
        });

        // Create a GardenPlant object for the standalone diagnosis
        const diagnosisPlant: GardenPlant = {
          id: savedDiagnosis.id,
          scientificName: plant.scientificName,
          commonName: plant.commonName,
          imageUrl: plant.imageUrl,
          description: plant.description,
          careInstructions: plant.careInstructions,
          tags: plant.tags,
          confidence: plant.confidence,
          addedDate: new Date(savedDiagnosis.created_at),
          createdAt: new Date(savedDiagnosis.created_at),
          updatedAt: new Date(savedDiagnosis.updated_at),
          healthStatus: mapSeverityToHealthStatus(savedDiagnosis.severity),
          isPublic: isPublic,
          plantType: plant.plantType,
          nativeRegion: plant.nativeRegion,
          toxicityLevel: plant.toxicityLevel,
          toxicityWarning: plant.toxicityWarning,
          growthHabit: plant.growthHabit,
          growthRate: plant.growthRate,
          matureHeight: plant.matureHeight,
          matureWidth: plant.matureWidth,
          matureDescription: plant.matureDescription,
          bloomTime: plant.bloomTime,
          flowerColors: plant.flowerColors,
          foliageType: plant.foliageType,
          hardinessZones: plant.hardinessZones,
          minTemperature: plant.minTemperature,
          pestsAndDiseases: plant.pestsAndDiseases,
          funFacts: plant.funFacts,
          uses: plant.uses,
          propagation: plant.propagation,
          seasonalCare: plant.seasonalCare,
          companionPlants: plant.companionPlants,
          maintenanceLevel: plant.maintenanceLevel,
          diagnosis: {
            id: savedDiagnosis.id,
            diagnosedProblem: savedDiagnosis.diagnosed_problem,
            likelyCauses: savedDiagnosis.likely_causes,
            symptomsObserved: savedDiagnosis.symptoms_observed,
            severity: savedDiagnosis.severity,
            immediateActions: savedDiagnosis.immediate_actions,
            longTermCare: savedDiagnosis.long_term_care,
            productRecommendations: savedDiagnosis.product_recommendations,
            stepByStepInstructions: savedDiagnosis.step_by_step_instructions,
            preventionTips: savedDiagnosis.prevention_tips,
            prognosis: savedDiagnosis.prognosis,
            createdAt: new Date(savedDiagnosis.created_at),
          },
        };

        // Add to the plants list
        setPlants((current) => [diagnosisPlant, ...current]);
      }
    } catch (error) {
      console.error('Error saving diagnosis:', error);
      throw error;
    }
  };

  const addPlantAndShare = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string, diagnosisData?: any) => {
    if (!user) return;

    try {
      // Get diagnosis data if diagnosisId is provided to determine health status
      let healthStatus: 'healthy' | 'sick' | 'recovering' | 'critical' = 'healthy';
      if (diagnosisId) {
        try {
          const diagnosis = await getPlantDiagnosis(diagnosisId);
          if (diagnosis) {
            healthStatus = mapSeverityToHealthStatus(diagnosis.severity);
          }
        } catch (error) {
          // Continue with default 'healthy' status
        }
      }

      // Create plant identification with garden fields (public)
      const identification = await createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: true, // Public - "Add to Garden & Showcase"
        location_taken: location,
        // Plant details
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
        // Garden fields (moved from garden_collections)
        notes: notes || '',
        nickname: nickname || plant.commonName,
        health_status: healthStatus,
        location_in_garden: location,
        allow_community_tips: true, // Public plants allow community tips
      });

      if (identification) {
        // Convert to GardenPlant and add to state
        const gardenPlant = convertIdentificationToGardenPlant(identification);
        setPlants((current) => [gardenPlant, ...current]);
        // console.log('Plant added to your garden and shared with the community successfully!');
      }
    } catch (error) {
      console.error('Error adding plant to garden and sharing:', error);
      throw error;
    }
  };



  const shareIdentificationOnly = async (plant: Plant) => {
    if (!user) return;

    try {
      await shareIdentificationOnly(plant, plant.imageUrl, user.id);
      // Show confirmation message
      // console.log('Plant identification shared with the community successfully!');
    } catch (error) {
      // Error sharing identification only
    }
  };

  const shareDiagnosisOnly = async (plant: Plant, diagnosisData: any) => {
    if (!user) return;

    try {
      await shareDiagnosisOnly(plant, plant.imageUrl, user.id, diagnosisData);
      // Show confirmation message
      // console.log('Plant diagnosis shared with the community successfully!');
    } catch (error) {
      // Error sharing diagnosis only
    }
  };

  // New functions specifically for diagnosis page
  const addDiagnosisToGarden = async (plant: Plant, nickname?: string, notes?: string, diagnosisData?: any, location?: string) => {
    if (!user) return;

    try {
      // Create diagnosis with complete plant data stored directly in plant_diagnoses table
      const savedDiagnosis = await createPlantDiagnosis({
        user_id: user.id,
        image_url: plant.imageUrl,
        problem_description: diagnosisData?.problemDescription,
        diagnosed_problem: diagnosisData?.diagnosedProblem,
        likely_causes: diagnosisData?.likelyCauses,
        symptoms_observed: diagnosisData?.symptomsObserved,
        severity: diagnosisData?.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical' || 'mild',
        immediate_actions: diagnosisData?.immediateActions,
        long_term_care: diagnosisData?.longTermCare,
        product_recommendations: diagnosisData?.productRecommendations,
        step_by_step_instructions: diagnosisData?.stepByStepInstructions,
        prevention_tips: diagnosisData?.preventionTips,
        prognosis: diagnosisData?.prognosis,
        confidence_score: diagnosisData?.confidence || 0.95,
        diagnosis_source: 'openrouter_api',
        is_verified: false,
        is_public: false, // Private - "Keep to myself"
        notes: notes,
        location: location,
        // Garden fields
        nickname: nickname || plant.commonName,
        health_status: mapSeverityToHealthStatus(diagnosisData?.severity),
        allow_community_tips: false, // Private diagnoses don't allow community tips
        // Plant identification fields stored directly
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: plant.careInstructions,
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
        tags: plant.tags,
      });

      // Convert to GardenPlant and add to state
      const gardenPlant = convertDiagnosisToGardenPlant(savedDiagnosis);
      setPlants((current) => [gardenPlant, ...current]);
      // console.log('Plant diagnosis saved to your garden successfully!');
    } catch (error) {
      console.error('Error saving diagnosis to garden:', error);
      throw error;
    }
  };

  const addDiagnosisAndShare = async (plant: Plant, nickname?: string, notes?: string, diagnosisData?: any, location?: string) => {
    if (!user) return;

    try {
      // Ensure valid severity and confidence_score
      let severity: 'mild' | 'moderate' | 'severe' | 'critical' = 'mild';
      if (diagnosisData?.severity && ['mild', 'moderate', 'severe', 'critical'].includes(diagnosisData.severity.toLowerCase())) {
        severity = diagnosisData.severity.toLowerCase();
      }
      const confidence_score = typeof diagnosisData?.confidence === 'number' && !isNaN(diagnosisData.confidence)
        ? diagnosisData.confidence
        : 0.95;

      // Create diagnosis with complete plant data stored directly in plant_diagnoses table
      const savedDiagnosis = await createPlantDiagnosis({
        user_id: user.id,
        image_url: plant.imageUrl,
        problem_description: diagnosisData?.problemDescription ?? '',
        diagnosed_problem: diagnosisData?.diagnosedProblem ?? '',
        likely_causes: diagnosisData?.likelyCauses ?? [],
        symptoms_observed: diagnosisData?.symptomsObserved ?? '',
        severity,
        immediate_actions: diagnosisData?.immediateActions ?? [],
        long_term_care: diagnosisData?.longTermCare ?? [],
        product_recommendations: diagnosisData?.productRecommendations ?? [],
        step_by_step_instructions: diagnosisData?.stepByStepInstructions ?? [],
        prevention_tips: diagnosisData?.preventionTips ?? [],
        prognosis: diagnosisData?.prognosis ?? '',
        confidence_score,
        diagnosis_source: 'openrouter_api',
        is_verified: false,
        is_public: true, // Public - "Add to Garden & Showcase"
        notes: notes ?? '',
        location: location ?? '',
        // Garden fields
        nickname: nickname || plant.commonName,
        health_status: mapSeverityToHealthStatus(severity),
        allow_community_tips: true, // Public diagnoses allow community tips
        // Plant identification fields stored directly
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: plant.careInstructions,
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
        tags: plant.tags,
      });

      // Convert to GardenPlant and add to state
      const gardenPlant = convertDiagnosisToGardenPlant(savedDiagnosis);
      setPlants((current) => [gardenPlant, ...current]);
      // console.log('Plant diagnosis saved to your garden and shared with the community successfully!');
    } catch (error) {
      console.error('Error saving and sharing diagnosis:', error);
      throw error;
    }
  };

  // New function to save diagnosis only to plant_diagnoses table
  const saveDiagnosisOnlyToTable = async (plant: Plant, notes?: string, diagnosisData?: any, location?: string) => {
    if (!user) return;

    try {
      // Create diagnosis with complete plant data stored directly in plant_diagnoses table
      const savedDiagnosis = await createPlantDiagnosis({
        user_id: user.id,
        image_url: plant.imageUrl,
        problem_description: diagnosisData?.problemDescription,
        diagnosed_problem: diagnosisData?.diagnosedProblem,
        likely_causes: diagnosisData?.likelyCauses,
        symptoms_observed: diagnosisData?.symptomsObserved,
        severity: diagnosisData?.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical' || 'mild',
        immediate_actions: diagnosisData?.immediateActions,
        long_term_care: diagnosisData?.longTermCare,
        product_recommendations: diagnosisData?.productRecommendations,
        step_by_step_instructions: diagnosisData?.stepByStepInstructions,
        prevention_tips: diagnosisData?.preventionTips,
        prognosis: diagnosisData?.prognosis,
        confidence_score: diagnosisData?.confidence || 0.95,
        diagnosis_source: 'openrouter_api',
        is_verified: false,
        is_public: false, // Private - "No! Keep to myself"
        notes: notes,
        location: location,
        // Garden fields
        nickname: plant.commonName,
        health_status: mapSeverityToHealthStatus(diagnosisData?.severity),
        allow_community_tips: false, // Private diagnoses don't allow community tips
        // Plant identification fields stored directly
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: plant.careInstructions,
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
        tags: plant.tags,
      });

      // Convert to GardenPlant and add to state
      const gardenPlant = convertDiagnosisToGardenPlant(savedDiagnosis);
      setPlants((current) => [gardenPlant, ...current]);
    } catch (error) {
      console.error('Error saving diagnosis only:', error);
      throw error;
    }
  };

  return {
    plants,
    isLoading,
    addPlant,
    addPlantAndShare,
    saveDiagnosisOnly,
    shareIdentificationOnly,
    shareDiagnosisOnly,
    removePlant,
    waterPlant,
    updatePlant,
    updateDiagnosisNotes: updateDiagnosisNotesLocal,
    shareGardenItem,
    mapSeverityToHealthStatus,
    // New diagnosis-specific functions
    addDiagnosisToGarden,
    addDiagnosisAndShare,
    saveDiagnosisOnlyToTable,
  };
});
