# Clerk + Supabase Native Integration

This app uses the **native Clerk + Supabase integration** approach, which is the recommended and most efficient way to integrate these services.

## How It Works

### 1. Native Third-Party Auth Provider
- Clerk acts as a third-party authentication provider for Supabase
- No webhooks needed - everything is handled through JWT tokens
- User profiles are created automatically using database defaults and RLS policies

### 2. Automatic User ID Mapping
- User profiles use `auth.jwt()->>'sub'` as the default value for `user_id`
- This automatically maps Clerk user IDs to Supabase user profiles
- RLS policies use `auth.jwt()->>'sub'` to identify the current user

### 3. Client-Side Integration
The integration uses a clean pattern in `lib/supabase.ts`:

```typescript
export const createClerkSupabaseClient = (getToken: () => Promise<string | null>): SupabaseClient => {
  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false,
    },
    global: {
      headers: {
        async Authorization() {
          const token = await getToken();
          return token ? `Bearer ${token}` : undefined;
        },
      },
    },
  });
};
```

### 4. Automatic Profile Creation
User profiles are created automatically when users first sign in using an upsert pattern:

```typescript
// In services/database.ts
static async ensureUserProfileExists(clerkUser: any, client?: SupabaseClient) {
  const profileData = {
    user_id: clerkUser.id,
    username: clerkUser.username || null,
    display_name: clerkUser.fullName || 'Plant Lover',
    avatar_url: clerkUser.imageUrl || null,
    // ... other default values
  };

  const { data: profile } = await client
    .from('user_profiles')
    .upsert(profileData, { onConflict: 'user_id' })
    .select()
    .single();

  return profile;
}
```

## Database Schema

### User Profiles Table
The `user_profiles` table has been configured with proper defaults:

```sql
-- user_id defaults to the Clerk user ID from JWT
ALTER TABLE public.user_profiles 
ALTER COLUMN user_id SET DEFAULT auth.jwt()->>'sub';

-- Other required fields have sensible defaults
ALTER TABLE public.user_profiles 
ALTER COLUMN display_name SET DEFAULT 'Plant Lover';
-- ... other defaults
```

### RLS Policies
All RLS policies use `auth.jwt()->>'sub'` to identify the current user:

```sql
CREATE POLICY "Users can insert their own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

CREATE POLICY "Users can view their own profile" ON public.user_profiles
  FOR SELECT USING (auth.jwt()->>'sub' = user_id OR is_public = true);
```

## Setup Requirements

### 1. Clerk Dashboard Configuration
1. Go to your Clerk dashboard
2. Navigate to JWT Templates
3. Create a new template named "supabase"
4. Configure it with your Supabase project details

### 2. Supabase Dashboard Configuration
1. Go to Authentication > Providers
2. Add Clerk as a third-party provider
3. Use your Clerk domain from the integration setup

### 3. Environment Variables
Make sure these are set in your app configuration:

```javascript
// app.config.js
export default {
  extra: {
    supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
    supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_KEY,
    clerkPublishableKey: process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY,
  }
};
```

## Benefits of Native Integration

1. **No Webhooks Required**: Eliminates the complexity and potential failure points of webhook synchronization
2. **Real-time Sync**: User data is always in sync because it comes directly from the JWT token
3. **Automatic Profile Creation**: User profiles are created seamlessly when users first interact with the app
4. **Better Performance**: Fewer API calls and database operations
5. **Simplified Architecture**: Less moving parts, easier to maintain and debug

## Migration from Webhook Approach

If migrating from a webhook-based approach:

1. ✅ Remove webhook functions (completed)
2. ✅ Update database schema with defaults (completed)
3. ✅ Update client-side integration (completed)
4. ✅ Implement automatic profile creation (completed)
5. ✅ Update RLS policies to use `auth.jwt()->>'sub'` (completed)

## Testing

To test the integration:

1. Sign up a new user through the app
2. Verify that a user profile is automatically created in the `user_profiles` table
3. Test that the user can create plant identifications and diagnoses
4. Verify that RLS policies work correctly (users can only see their own data)

## Troubleshooting

### Common Issues

1. **JWT Template Not Configured**: Make sure the "supabase" JWT template is properly configured in Clerk
2. **RLS Policy Errors**: Ensure all policies use `auth.jwt()->>'sub'` instead of `auth.uid()`
3. **Profile Creation Fails**: Check that the database migration has been applied correctly

### Debug Steps

1. Check browser/app console for JWT token errors
2. Verify that the Clerk user object contains the expected fields
3. Test database operations directly in Supabase dashboard
4. Ensure environment variables are properly set
