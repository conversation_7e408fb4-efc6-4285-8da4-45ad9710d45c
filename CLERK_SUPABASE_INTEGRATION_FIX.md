# Clerk + Supabase Integration Fix

## Issues Identified

1. **User profiles not syncing**: Clerk webhook edge function exists but is not deployed to Supabase
2. **Console errors**: JWT token authentication issues causing 401 Unauthorized errors
3. **Plant identification/diagnosis saves failing**: RLS policies expecting user profiles that don't exist

## Root Causes

1. **Missing Webhook Deployment**: The `supabase/functions/clerk-webhook/index.ts` exists but isn't deployed
2. **JWT Template Configuration**: Clerk's JWT template for Supabase may not be properly configured
3. **Project Mismatch**: App uses staging project (xvgmitjuwgmvllgompsm) but some configs reference live project

## Solution Steps

### 1. Deploy Clerk Webhook to Supabase

The webhook function needs to be deployed to handle user.created, user.updated, and user.deleted events.

**IMMEDIATE ACTION REQUIRED:**

**Manual deployment via Supabase Dashboard:**
1. Go to https://supabase.com/dashboard/project/xvgmitjuwgmvllgompsm/functions
2. Click "Create a new function"
3. Name: `clerk-webhook`
4. Copy the entire content from `supabase/functions/clerk-webhook/index.ts`
5. Deploy the function
6. Go to Settings > Environment variables and add:
   - `SUPABASE_URL`: `https://xvgmitjuwgmvllgompsm.supabase.co`
   - `SUPABASE_SERVICE_ROLE_KEY`: (get from Project Settings > API > service_role key)

**Function URL will be:** `https://xvgmitjuwgmvllgompsm.supabase.co/functions/v1/clerk-webhook`

### 2. Configure Clerk JWT Template

In Clerk Dashboard:
1. Go to JWT Templates
2. Create/edit "supabase" template
3. Configure claims:
```json
{
  "aud": "authenticated",
  "exp": {{exp}},
  "iat": {{iat}},
  "iss": "https://xvgmitjuwgmvllgompsm.supabase.co/auth/v1",
  "sub": "{{user.id}}",
  "email": "{{user.primary_email_address.email_address}}",
  "phone": "",
  "app_metadata": {
    "provider": "clerk",
    "providers": ["clerk"]
  },
  "user_metadata": {
    "full_name": "{{user.full_name}}",
    "avatar_url": "{{user.image_url}}"
  },
  "role": "authenticated"
}
```

### 3. Configure Clerk Webhook

In Clerk Dashboard:
1. Go to Webhooks
2. Create new webhook endpoint: `https://xvgmitjuwgmvllgompsm.supabase.co/functions/v1/clerk-webhook`
3. Subscribe to events:
   - user.created
   - user.updated
   - user.deleted
4. Copy the signing secret for Supabase environment variables

### 4. Fix Authentication Issues

The main issue is that users don't have profiles in the database, causing RLS policy failures.

**Immediate fix**: Create a fallback mechanism to create user profiles on-demand when they don't exist.

### 5. Test the Integration

1. Create a new test user via Google OAuth
2. Verify user profile is created in `user_profiles` table
3. Test plant identification/diagnosis saving
4. Check console for remaining errors

## Files Updated (COMPLETED)

1. ✅ `services/database.ts` - Added `ensureUserProfileExists()` fallback method
2. ✅ `lib/supabase.ts` - Added better error handling for token issues
3. ✅ `hooks/useAuth.tsx` - Added better error logging for JWT template issues

## Immediate Fixes Applied

1. **User Profile Fallback**: Added automatic user profile creation when missing
2. **Better Error Handling**: Improved error messages for authentication issues
3. **JWT Token Debugging**: Added logging to identify template configuration issues

These fixes should resolve the immediate console errors and allow plant identification/diagnosis saving to work even without the webhook deployed.

## Environment Variables Needed

In Supabase Edge Function:
- `SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`
- `CLERK_WEBHOOK_SECRET` (from Clerk webhook configuration)
